'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Loader } from '@googlemaps/js-api-loader'
import { MapPin, ExternalLink } from 'lucide-react'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from './npi-card'

interface NPIGoogleMapProps {
  title?: string
  description?: string
  className?: string
  height?: string
  apiKey?: string
}

export const NPIGoogleMap: React.FC<NPIGoogleMapProps> = ({
  title = 'Find Us',
  description = 'Visit our office at the National Museums of Kenya, Museum Hill Road, Nairobi.',
  className = '',
  height = 'h-96',
  apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)

  // NPI Office coordinates (National Museums of Kenya, Museum Hill Road, Nairobi)
  const npiLocation = {
    lat: -1.2741,
    lng: 36.8155,
  }

  useEffect(() => {
    if (!apiKey) {
      setMapError('Google Maps API key not configured')
      return
    }

    const initMap = async () => {
      try {
        const loader = new Loader({
          apiKey: apiKey,
          version: 'weekly',
          libraries: ['places'],
        })

        const { Map } = await loader.importLibrary('maps')
        const { AdvancedMarkerElement } = await loader.importLibrary('marker')

        if (mapRef.current) {
          const map = new Map(mapRef.current, {
            center: npiLocation,
            zoom: 15,
            mapId: 'npi-office-map',
            styles: [
              {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }],
              },
              {
                featureType: 'road',
                elementType: 'geometry',
                stylers: [{ color: '#f5f1eb' }],
              },
              {
                featureType: 'water',
                elementType: 'geometry',
                stylers: [{ color: '#c9c2bc' }],
              },
            ],
          })

          // Create custom marker
          const marker = new AdvancedMarkerElement({
            map: map,
            position: npiLocation,
            title: 'Natural Products Industry Initiative - NPI Office',
          })

          // Create info window
          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div style="padding: 12px; font-family: 'Inter', sans-serif;">
                <h3 style="margin: 0 0 8px 0; color: #725242; font-size: 16px; font-weight: 600;">
                  Natural Products Industry Initiative
                </h3>
                <p style="margin: 0 0 8px 0; color: #725242; font-size: 14px;">
                  National Museums of Kenya<br>
                  Museum Hill Road<br>
                  P.O. Box 40658-00100<br>
                  Nairobi, Kenya
                </p>
                <div style="margin-top: 12px;">
                  <a 
                    href="https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    style="color: #A8422D; text-decoration: none; font-size: 14px; font-weight: 500;"
                  >
                    Get Directions →
                  </a>
                </div>
              </div>
            `,
          })

          // Open info window on marker click
          marker.addListener('click', () => {
            infoWindow.open(map, marker)
          })

          setMapLoaded(true)
        }
      } catch (error) {
        console.error('Error loading Google Maps:', error)
        setMapError('Failed to load map. Please try again later.')
      }
    }

    initMap()
  }, [apiKey])

  if (mapError) {
    return (
      <NPICard className={`bg-white border-2 border-[#725242]/20 ${className}`}>
        <NPICardHeader>
          <div className="flex items-center gap-3">
            <MapPin className="w-6 h-6 text-[#A8422D]" />
            <NPICardTitle className="text-xl font-bold text-[#725242] font-npi">
              {title}
            </NPICardTitle>
          </div>
          {description && (
            <p className="text-[#725242]/80 font-npi mt-2">{description}</p>
          )}
        </NPICardHeader>
        <NPICardContent>
          <div className={`${height} bg-[#FFF9E1] border-2 border-[#A8422D]/20 flex items-center justify-center`}>
            <div className="text-center p-6">
              <MapPin className="w-12 h-12 text-[#A8422D] mx-auto mb-4" />
              <p className="text-[#725242] font-npi font-semibold mb-2">Map Unavailable</p>
              <p className="text-sm text-[#725242]/80 font-npi mb-4">{mapError}</p>
              <a
                href={`https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-[#A8422D] hover:text-[#725242] transition-colors duration-200 font-npi font-medium"
              >
                <ExternalLink className="w-4 h-4" />
                Open in Google Maps
              </a>
            </div>
          </div>
        </NPICardContent>
      </NPICard>
    )
  }

  return (
    <NPICard className={`bg-white border-2 border-[#725242]/20 ${className}`}>
      <NPICardHeader>
        <div className="flex items-center gap-3">
          <MapPin className="w-6 h-6 text-[#A8422D]" />
          <NPICardTitle className="text-xl font-bold text-[#725242] font-npi">
            {title}
          </NPICardTitle>
        </div>
        {description && (
          <p className="text-[#725242]/80 font-npi mt-2">{description}</p>
        )}
      </NPICardHeader>
      <NPICardContent>
        <div className={`${height} bg-[#FFF9E1] border-2 border-[#A8422D]/20 overflow-hidden`}>
          {!mapLoaded && (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-8 h-8 border-2 border-[#A8422D] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-[#725242] font-npi">Loading map...</p>
              </div>
            </div>
          )}
          <div ref={mapRef} className="w-full h-full" />
        </div>
        <div className="mt-4 flex justify-between items-center">
          <div className="text-sm text-[#725242]/80 font-npi">
            National Museums of Kenya, Museum Hill Road
          </div>
          <a
            href={`https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 text-[#A8422D] hover:text-[#725242] transition-colors duration-200 font-npi font-medium text-sm"
          >
            <ExternalLink className="w-4 h-4" />
            Get Directions
          </a>
        </div>
      </NPICardContent>
    </NPICard>
  )
}
