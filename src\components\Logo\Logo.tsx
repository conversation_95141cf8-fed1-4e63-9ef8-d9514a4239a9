import clsx from 'clsx'
import React from 'react'
import Image from 'next/image'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const Logo = (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  return (
    <div className={clsx('flex items-center gap-3', className)}>
      {/* NPI Logo */}
      <div className="relative h-10 w-16">
        <Image
          src="/assets/logo_npi.png"
          alt="Natural Products Industry Initiative Logo"
          fill
          className="object-contain"
          loading={loading}
          priority={priority === 'high'}
        />
      </div>

      {/* Vision 2030 Logo */}
      <div className="relative h-10 w-16">
        <Image
          src="/assets/logo_2030.png"
          alt="Kenya Vision 2030 Logo"
          fill
          className="object-contain"
          loading={loading}
          priority={priority === 'high'}
        />
      </div>

      {/* Text */}
      <div className="flex flex-col">
        <div className="font-npi font-bold text-lg text-white leading-tight">NPI</div>
        <div className="font-npi text-xs text-white leading-tight">Natural Products Initiative</div>
      </div>
    </div>
  )
}
