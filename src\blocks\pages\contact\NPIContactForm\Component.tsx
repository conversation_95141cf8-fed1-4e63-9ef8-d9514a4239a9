'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { NPIFAQ } from '@/components/ui/npi-faq'
import { NPIContactInfo } from '@/components/ui/npi-contact-info'
import { NPIGoogleMap } from '@/components/ui/npi-google-map'
import { Mail, Send, User, MessageSquare } from 'lucide-react'

interface NPIContactFormProps {
  title?: string
  description?: string
}

export const NPIContactFormBlock: React.FC<NPIContactFormProps> = ({
  title = 'Contact Us',
  description = "Get in touch with the NPI team. We're here to answer your questions, provide information, and explore collaboration opportunities.",
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    category: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Reset form
    setFormData({
      name: '',
      email: '',
      organization: '',
      subject: '',
      category: '',
      message: '',
    })
    setIsSubmitting(false)
    alert('Thank you for your message! We will get back to you soon.')
  }

  const categories = [
    'General Inquiry',
    'Partnership Opportunity',
    'Research Collaboration',
    'Media & Press',
    'IKIA Database Access',
    'Program Information',
    'Technical Support',
    'Other',
  ]

  return (
    <>
      {/* Main Contact Section */}
      <NPISection className="bg-white pt-0">
        <NPISectionHeader>
          <NPISectionTitle className="text-[#725242]">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242]/80">{description}</NPISectionDescription>
        </NPISectionHeader>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Contact Form - Left Side */}
          <div>
            <NPICard className="bg-white border-2 border-[#725242]/20 shadow-lg">
              <NPICardHeader className="bg-[#A8422D] p-6">
                <NPICardTitle className="text-2xl flex items-center gap-3 text-white">
                  <MessageSquare className="w-6 h-6" />
                  Send us a Message
                </NPICardTitle>
              </NPICardHeader>
              <NPICardContent className="p-5">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                      >
                        Full Name *
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#A8422D] w-5 h-5" />
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          className="w-full pl-10 pr-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300"
                          placeholder="Enter your full name"
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                      >
                        Email Address *
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#A8422D] w-5 h-5" />
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full pl-10 pr-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300"
                          placeholder="Enter your email address"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="organization"
                        className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                      >
                        Organization
                      </label>
                      <input
                        type="text"
                        id="organization"
                        name="organization"
                        value={formData.organization}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300"
                        placeholder="Your organization (optional)"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="category"
                        className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                      >
                        Category *
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300"
                      >
                        <option value="">Select a category</option>
                        {categories.map((category) => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                    >
                      Subject *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300"
                      placeholder="Brief subject of your message"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-bold mb-2 font-npi text-[#725242]"
                    >
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 border-2 border-[#725242]/20 focus:border-[#A8422D] bg-white focus:outline-none focus:ring-2 focus:ring-[#A8422D]/20 font-npi transition-all duration-300 resize-vertical"
                      placeholder="Please provide details about your inquiry..."
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <NPIButton
                      type="submit"
                      variant="primary"
                      size="lg"
                      disabled={isSubmitting}
                      className="bg-[#A8422D] hover:bg-[#725242] border-2 border-[#A8422D] hover:border-[#725242] shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-5 h-5 mr-2" />
                          Send Message
                        </>
                      )}
                    </NPIButton>

                    <p className="text-sm text-[#725242]/80 font-npi">
                      We typically respond within 24-48 hours
                    </p>
                  </div>
                </form>
              </NPICardContent>
            </NPICard>
          </div>

          {/* Map - Right Side */}
          <div>
            <NPIGoogleMap
              title="Find Us"
              description="Visit our office at the National Museums of Kenya, Museum Hill Road, Nairobi."
              height="h-[500px]"
            />
          </div>
        </div>
      </NPISection>

      {/* FAQ Section */}
      <NPISection className="bg-[#FFF9E1] py-12">
        <NPIFAQ />
      </NPISection>

      {/* Contact Information Section */}
      <NPISection className="bg-white py-12">
        <NPIContactInfo />
      </NPISection>
    </>
  )
}
