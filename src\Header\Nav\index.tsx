'use client'

import React, { useState } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Menu, X, ChevronDown } from 'lucide-react'

import type { Header as HeaderType } from '@/payload-types'

// Define the main navigation structure for NPI - only includes actual existing pages
const mainNavItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'About',
    href: '/about',
    children: [
      { label: 'Strategic Pillars', href: '/strategic-pillars' },
      { label: 'Strategic Alignment', href: '/about/strategic-alignment' },
      { label: 'Operations & Structure', href: '/about/operations-structure' },
    ],
  },
  {
    label: 'Projects',
    href: '/projects',
  },
  {
    label: 'Success Stories',
    href: '/success-stories',
  },
  {
    label: 'IKIA Database',
    href: 'http://inkibank.museums.or.ke:8185/',
  },
  {
    label: 'Resources',
    href: '/resources',
    children: [
      { label: 'Events', href: '/events' },
      { label: 'News & Updates', href: '/news' },
      { label: 'Media Gallery', href: '/resources/media-gallery' },
    ],
  },
  {
    label: 'Partnerships',
    href: '/partnerships',
    children: [
      { label: 'Investment Opportunities', href: '/partnerships/investment-opportunities' },
      { label: 'Our Partners', href: '/partnerships/partners' },
    ],
  },
]

export const HeaderNav: React.FC<{ data: HeaderType }> = ({ data: _data }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const pathname = usePathname()

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
    setActiveDropdown(null)
  }

  const toggleDropdown = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label)
  }

  const isActiveLink = (href: string) => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center flex-1">
        {/* Spacer for balance */}
        <div className="flex-1"></div>

        {/* Centered Navigation Links */}
        <div className="flex items-center gap-1">
          {mainNavItems.map((item) => (
            <div key={item.label} className="relative group">
              {item.href.startsWith('http') ? (
                <a
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`
                    px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                    flex items-center gap-1 hover:bg-white/10 hover:text-white
                    text-white hover:text-white
                  `}
                  onMouseEnter={() => item.children && setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  {item.label}
                  {item.children && (
                    <ChevronDown className="w-3 h-3 transition-transform duration-200 group-hover:rotate-180" />
                  )}
                </a>
              ) : (
                <Link
                  href={item.href}
                  className={`
                    px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                    flex items-center gap-1 hover:bg-white/10 hover:text-white
                    ${
                      isActiveLink(item.href)
                        ? 'text-white bg-white/10'
                        : 'text-white hover:text-white'
                    }
                  `}
                  onMouseEnter={() => item.children && setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  {item.label}
                  {item.children && (
                    <ChevronDown className="w-3 h-3 transition-transform duration-200 group-hover:rotate-180" />
                  )}
                </Link>
              )}

              {/* Desktop Dropdown */}
              {item.children && (
                <div
                  className={`
                  absolute top-full left-0 mt-1 w-56 bg-white border border-border rounded-lg shadow-lg
                  transition-all duration-200 z-50
                  ${
                    activeDropdown === item.label
                      ? 'opacity-100 visible translate-y-0'
                      : 'opacity-0 invisible -translate-y-2'
                  }
                `}
                  onMouseEnter={() => setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <div className="py-2">
                    {item.children.map((child) => (
                      <Link
                        key={child.href}
                        href={child.href}
                        className={`
                        block px-4 py-2 text-sm transition-colors duration-150
                        ${
                          isActiveLink(child.href)
                            ? 'text-primary bg-primary/5 border-r-2 border-primary'
                            : 'text-foreground hover:text-primary hover:bg-primary/5'
                        }
                      `}
                      >
                        {child.label}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact Button - Right aligned */}
        <div className="flex-1 flex justify-end">
          <Link
            href="/contact"
            className="px-6 py-2 bg-white/10 text-white rounded-md text-sm font-medium transition-all duration-200 hover:bg-white/20 hover:shadow-md backdrop-blur-sm"
          >
            Contact
          </Link>
        </div>
      </nav>

      {/* Mobile Menu Button */}
      <button
        onClick={toggleMobileMenu}
        className="lg:hidden p-2 rounded-md hover:bg-white/10 transition-colors duration-200"
        aria-label="Toggle mobile menu"
      >
        {isMobileMenuOpen ? (
          <X className="w-6 h-6 text-white" />
        ) : (
          <Menu className="w-6 h-6 text-white" />
        )}
      </button>

      {/* Mobile Navigation Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 bg-black/50" onClick={toggleMobileMenu}>
          <div
            className="absolute top-0 right-0 w-80 max-w-[90vw] h-full bg-white shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              {/* Mobile Menu Header */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-primary">Navigation</h2>
                <button
                  onClick={toggleMobileMenu}
                  className="p-2 rounded-md hover:bg-primary/5 transition-colors duration-200"
                >
                  <X className="w-5 h-5 text-primary" />
                </button>
              </div>

              {/* Mobile Menu Items */}
              <nav className="space-y-2">
                {mainNavItems.map((item) => (
                  <div key={item.label}>
                    <div className="flex items-center justify-between">
                      {item.href.startsWith('http') ? (
                        <a
                          href={item.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          onClick={toggleMobileMenu}
                          className={`
                            flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200
                            text-foreground hover:text-primary hover:bg-primary/5
                          `}
                        >
                          {item.label}
                        </a>
                      ) : (
                        <Link
                          href={item.href}
                          onClick={toggleMobileMenu}
                          className={`
                            flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200
                            ${
                              isActiveLink(item.href)
                                ? 'text-primary bg-primary/5'
                                : 'text-foreground hover:text-primary hover:bg-primary/5'
                            }
                          `}
                        >
                          {item.label}
                        </Link>
                      )}
                      {item.children && (
                        <button
                          onClick={() => toggleDropdown(item.label)}
                          className="p-2 rounded-md hover:bg-primary/5 transition-colors duration-200"
                        >
                          <ChevronDown
                            className={`w-4 h-4 text-primary transition-transform duration-200 ${
                              activeDropdown === item.label ? 'rotate-180' : ''
                            }`}
                          />
                        </button>
                      )}
                    </div>

                    {/* Mobile Dropdown */}
                    {item.children && activeDropdown === item.label && (
                      <div className="ml-4 mt-2 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            onClick={toggleMobileMenu}
                            className={`
                              block px-3 py-2 rounded-md text-sm transition-colors duration-200
                              ${
                                isActiveLink(child.href)
                                  ? 'text-primary bg-primary/5 border-l-2 border-primary'
                                  : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                              }
                            `}
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
